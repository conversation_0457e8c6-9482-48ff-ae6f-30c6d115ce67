import { useState, useEffect } from "react";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faSearch, faFilter } from "@fortawesome/free-solid-svg-icons";
import TheSanPham from "./TheSanPham";
import { useGioHang } from "../context/GioHangContext";
import "./DanhSachSanPham.css";

const DanhSachSanPham = () => {
  const { tongSoLuong } = useGioHang();
  const [danhSachSanPham, setDanhSachSanPham] = useState([]);
  const [dangTai, setDangTai] = useState(true);
  const [loi, setLoi] = useState(null);
  const [boLocDanhMuc, setBoLocDanhMuc] = useState("Tất cả");
  const [tuKhoaTimKiem, setTuKhoaTimKiem] = useState("");

  // L<PERSON>y dữ liệu sản phẩm từ file JSON
  useEffect(() => {
    const layDuLieuSanPham = async () => {
      try {
        setDangTai(true);
        const phanHoi = await fetch("/duLieuSanPham.json");

        if (!phanHoi.ok) {
          throw new Error("Không thể tải dữ liệu sản phẩm");
        }

        const duLieu = await phanHoi.json();
        setDanhSachSanPham(duLieu);
        setLoi(null);
      } catch (error) {
        console.error("Lỗi khi tải dữ liệu:", error);
        setLoi("Có lỗi xảy ra khi tải dữ liệu sản phẩm");
      } finally {
        setDangTai(false);
      }
    };

    layDuLieuSanPham();
  }, []);

  // Lấy danh sách danh mục duy nhất
  const danhSachDanhMuc = [
    "Tất cả",
    ...new Set(danhSachSanPham.map((sp) => sp.danhMuc)),
  ];

  // Lọc sản phẩm theo danh mục và từ khóa tìm kiếm
  const sanPhamDaLoc = danhSachSanPham.filter((sanPham) => {
    const khopDanhMuc =
      boLocDanhMuc === "Tất cả" || sanPham.danhMuc === boLocDanhMuc;
    const khopTimKiem =
      sanPham.tenSanPham.toLowerCase().includes(tuKhoaTimKiem.toLowerCase()) ||
      sanPham.moTa.toLowerCase().includes(tuKhoaTimKiem.toLowerCase());
    return khopDanhMuc && khopTimKiem;
  });

  if (dangTai) {
    return (
      <div className="container-danh-sach">
        <div className="trang-thai-tai">
          <div className="spinner"></div>
          <p>Đang tải danh sách sản phẩm...</p>
        </div>
      </div>
    );
  }

  if (loi) {
    return (
      <div className="container-danh-sach">
        <div className="thong-bao-loi">
          <h2>Có lỗi xảy ra</h2>
          <p>{loi}</p>
          <button onClick={() => window.location.reload()}>Thử lại</button>
        </div>
      </div>
    );
  }

  return (
    <div className="container-danh-sach">
      <header className="header-danh-sach">
        <h1>Danh Sách Sản Phẩm</h1>
        <p>Khám phá các sản phẩm công nghệ mới nhất</p>
      </header>

      <div className="thanh-dieu-khien">
        <div className="tim-kiem">
          <div className="o-tim-kiem-container">
            <FontAwesomeIcon icon={faSearch} className="icon-tim-kiem" />
            <input
              type="text"
              placeholder="Tìm kiếm sản phẩm..."
              value={tuKhoaTimKiem}
              onChange={(e) => setTuKhoaTimKiem(e.target.value)}
              className="o-tim-kiem"
            />
          </div>
        </div>

        <div className="bo-loc-danh-muc">
          <div className="select-container">
            <FontAwesomeIcon icon={faFilter} className="icon-filter" />
            <select
              value={boLocDanhMuc}
              onChange={(e) => setBoLocDanhMuc(e.target.value)}
              className="select-danh-muc"
            >
              {danhSachDanhMuc.map((danhMuc) => (
                <option key={danhMuc} value={danhMuc}>
                  {danhMuc}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      <div className="thong-tin-ket-qua">
        <p>Hiển thị {sanPhamDaLoc.length} sản phẩm</p>
      </div>

      {sanPhamDaLoc.length === 0 ? (
        <div className="khong-co-san-pham">
          <h3>Không tìm thấy sản phẩm nào</h3>
          <p>Hãy thử thay đổi từ khóa tìm kiếm hoặc bộ lọc danh mục</p>
        </div>
      ) : (
        <div className="luoi-san-pham">
          {sanPhamDaLoc.map((sanPham) => (
            <TheSanPham key={sanPham.id} sanPham={sanPham} />
          ))}
        </div>
      )}
    </div>
  );
};

export default DanhSachSanPham;
