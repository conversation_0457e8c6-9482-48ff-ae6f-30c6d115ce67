.container-danh-sach {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.header-danh-sach {
  text-align: center;
  margin-bottom: 40px;
}

.header-danh-sach h1 {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 10px;
  font-weight: 700;
}

.header-danh-sach p {
  font-size: 1.1rem;
  color: #666;
  margin: 0;
}

.thanh-dieu-khien {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  flex-wrap: wrap;
  align-items: center;
}

.tim-kiem {
  flex: 1;
  min-width: 250px;
}

.o-tim-kiem-container {
  position: relative;
  width: 100%;
}

.icon-tim-kiem {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  z-index: 1;
}

.o-tim-kiem {
  width: 100%;
  padding: 12px 16px 12px 40px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.o-tim-kiem:focus {
  outline: none;
  border-color: #007bff;
}

.o-tim-kiem:focus+.icon-tim-kiem {
  color: #007bff;
}

.bo-loc-danh-muc {
  min-width: 200px;
}

.select-container {
  position: relative;
  width: 100%;
}

.icon-filter {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #666;
  z-index: 1;
  pointer-events: none;
}

.select-danh-muc {
  width: 100%;
  padding: 12px 16px 12px 40px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 1rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 12px center;
  background-repeat: no-repeat;
  background-size: 16px;
}

.select-danh-muc:focus {
  outline: none;
  border-color: #007bff;
}

.select-danh-muc:focus+.icon-filter {
  color: #007bff;
}

.thong-tin-ket-qua {
  margin-bottom: 20px;
}

.thong-tin-ket-qua p {
  color: #666;
  font-size: 0.95rem;
  margin: 0;
}

.luoi-san-pham {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.trang-thai-tai {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.trang-thai-tai p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

.thong-bao-loi {
  text-align: center;
  padding: 60px 20px;
}

.thong-bao-loi h2 {
  color: #e74c3c;
  margin-bottom: 15px;
}

.thong-bao-loi p {
  color: #666;
  margin-bottom: 25px;
}

.thong-bao-loi button {
  padding: 12px 24px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.thong-bao-loi button:hover {
  background: #0056b3;
}

.khong-co-san-pham {
  text-align: center;
  padding: 60px 20px;
}

.khong-co-san-pham h3 {
  color: #333;
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.khong-co-san-pham p {
  color: #666;
  font-size: 1rem;
  margin: 0;
}

@media (max-width: 768px) {
  .container-danh-sach {
    padding: 15px;
  }

  .header-danh-sach h1 {
    font-size: 2rem;
  }

  .thanh-dieu-khien {
    flex-direction: column;
    gap: 15px;
  }

  .tim-kiem,
  .bo-loc-danh-muc {
    min-width: 100%;
  }

  .luoi-san-pham {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .header-danh-sach h1 {
    font-size: 1.75rem;
  }

  .header-danh-sach p {
    font-size: 1rem;
  }
}