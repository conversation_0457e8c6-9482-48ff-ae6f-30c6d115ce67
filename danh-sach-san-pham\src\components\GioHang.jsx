import { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faShoppingCart, 
  faTimes, 
  faPlus, 
  faMinus, 
  faTrash,
  faShoppingBag
} from '@fortawesome/free-solid-svg-icons';
import { useGioHang } from '../context/GioHangContext';
import './GioHang.css';

const GioHang = () => {
  const {
    danhSachSanPham,
    tongSoLuong,
    tongTien,
    xoaKhoiGio,
    capNhatSoLuong,
    xoaToanBo
  } = useGioHang();

  const [dangMo, setDangMo] = useState(false);

  // Hàm định dạng giá tiền
  const dinhDangGia = (gia) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(gia);
  };

  // Hàm xử lý tăng số lượng
  const tangSoLuong = (id, soLuongHienTai, tonKho) => {
    if (soLuongHienTai < tonKho) {
      capNhatSoLuong(id, soLuongHienTai + 1);
    }
  };

  // Hàm xử lý giảm số lượng
  const giamSoLuong = (id, soLuongHienTai) => {
    if (soLuongHienTai > 1) {
      capNhatSoLuong(id, soLuongHienTai - 1);
    }
  };

  // Hàm xử lý thanh toán
  const xuLyThanhToan = () => {
    alert(`Tổng tiền: ${dinhDangGia(tongTien)}\nCảm ơn bạn đã mua hàng!`);
    xoaToanBo();
    setDangMo(false);
  };

  return (
    <>
      {/* Nút mở giỏ hàng */}
      <div className="nut-gio-hang" onClick={() => setDangMo(true)}>
        <FontAwesomeIcon icon={faShoppingCart} />
        {tongSoLuong > 0 && (
          <span className="so-luong-badge">{tongSoLuong}</span>
        )}
      </div>

      {/* Overlay */}
      {dangMo && (
        <div className="gio-hang-overlay" onClick={() => setDangMo(false)} />
      )}

      {/* Sidebar giỏ hàng */}
      <div className={`gio-hang-sidebar ${dangMo ? 'mo' : ''}`}>
        <div className="gio-hang-header">
          <h2>
            <FontAwesomeIcon icon={faShoppingBag} />
            Giỏ hàng của bạn
          </h2>
          <button 
            className="nut-dong"
            onClick={() => setDangMo(false)}
          >
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>

        <div className="gio-hang-noi-dung">
          {danhSachSanPham.length === 0 ? (
            <div className="gio-hang-trong">
              <FontAwesomeIcon icon={faShoppingCart} size="3x" />
              <p>Giỏ hàng của bạn đang trống</p>
              <button 
                className="nut-tiep-tuc-mua"
                onClick={() => setDangMo(false)}
              >
                Tiếp tục mua sắm
              </button>
            </div>
          ) : (
            <>
              <div className="danh-sach-san-pham-gio">
                {danhSachSanPham.map(sanPham => (
                  <div key={sanPham.id} className="san-pham-gio">
                    <img 
                      src={sanPham.hinhAnh} 
                      alt={sanPham.tenSanPham}
                      className="hinh-anh-gio"
                      onError={(e) => {
                        e.target.src = 'https://via.placeholder.com/80x80?text=Không+có+hình';
                      }}
                    />
                    
                    <div className="thong-tin-gio">
                      <h4 className="ten-san-pham-gio">{sanPham.tenSanPham}</h4>
                      <p className="gia-gio">{dinhDangGia(sanPham.gia)}</p>
                      
                      <div className="dieu-khien-so-luong">
                        <button 
                          className="nut-so-luong"
                          onClick={() => giamSoLuong(sanPham.id, sanPham.soLuong)}
                          disabled={sanPham.soLuong <= 1}
                        >
                          <FontAwesomeIcon icon={faMinus} />
                        </button>
                        
                        <span className="so-luong-hien-thi">{sanPham.soLuong}</span>
                        
                        <button 
                          className="nut-so-luong"
                          onClick={() => tangSoLuong(sanPham.id, sanPham.soLuong, sanPham.tonKho)}
                          disabled={sanPham.soLuong >= sanPham.tonKho}
                        >
                          <FontAwesomeIcon icon={faPlus} />
                        </button>
                      </div>
                    </div>
                    
                    <div className="gia-va-xoa">
                      <p className="tong-gia-san-pham">
                        {dinhDangGia(sanPham.gia * sanPham.soLuong)}
                      </p>
                      <button 
                        className="nut-xoa"
                        onClick={() => xoaKhoiGio(sanPham.id)}
                      >
                        <FontAwesomeIcon icon={faTrash} />
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              <div className="gio-hang-footer">
                <div className="tong-tien">
                  <h3>Tổng cộng: {dinhDangGia(tongTien)}</h3>
                </div>
                
                <div className="cac-nut-hanh-dong">
                  <button 
                    className="nut-xoa-toan-bo"
                    onClick={xoaToanBo}
                  >
                    <FontAwesomeIcon icon={faTrash} />
                    Xóa tất cả
                  </button>
                  
                  <button 
                    className="nut-thanh-toan"
                    onClick={xuLyThanhToan}
                  >
                    <FontAwesomeIcon icon={faShoppingBag} />
                    Thanh toán
                  </button>
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default GioHang;
