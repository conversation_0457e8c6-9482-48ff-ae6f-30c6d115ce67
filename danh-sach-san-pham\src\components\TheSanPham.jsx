import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import {
  faShoppingCart,
  faCheck,
  faStar,
  faStarHalfAlt,
  faShoppingBag,
  faPercent,
  faUsers,
} from "@fortawesome/free-solid-svg-icons";
import { useGioHang } from "../context/GioHangContext";
import { useState } from "react";
import "./TheSanPham.css";

const TheSanPham = ({ sanPham }) => {
  const { themVaoGio, danhSachSanPham } = useGioHang();
  const [vuaThemVaoGio, setVuaThemVaoGio] = useState(false);
  // Hàm định dạng giá tiền
  const dinhDangGia = (gia) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(gia);
  };

  // Hàm định dạng số lượng đã bán
  const dinhDangSoLuongDaBan = (soLuong) => {
    if (soLuong >= 1000) {
      return `${(soLuong / 1000).toFixed(1)}k`;
    }
    return soLuong.toString();
  };

  // Hàm tạo sao đánh giá
  const taoSaoDanhGia = (diem) => {
    const saoDay = [];
    const saoNguyen = Math.floor(diem);
    const coSaoNua = diem % 1 !== 0;

    // Thêm sao đầy
    for (let i = 0; i < saoNguyen; i++) {
      saoDay.push(
        <FontAwesomeIcon key={`full-${i}`} icon={faStar} className="sao-day" />
      );
    }

    // Thêm sao nửa nếu có
    if (coSaoNua) {
      saoDay.push(
        <FontAwesomeIcon key="half" icon={faStarHalfAlt} className="sao-nua" />
      );
    }

    // Thêm sao rỗng
    const saoConLai = 5 - Math.ceil(diem);
    for (let i = 0; i < saoConLai; i++) {
      saoDay.push(
        <FontAwesomeIcon
          key={`empty-${i}`}
          icon={faStar}
          className="sao-rong"
        />
      );
    }

    return saoDay;
  };

  // Kiểm tra xem sản phẩm đã có trong giỏ hàng chưa
  const sanPhamTrongGio = danhSachSanPham.find(
    (item) => item.id === sanPham.id
  );

  // Hàm xử lý thêm vào giỏ hàng
  const xuLyThemVaoGio = () => {
    themVaoGio(sanPham);
    setVuaThemVaoGio(true);

    // Reset trạng thái sau 2 giây
    setTimeout(() => {
      setVuaThemVaoGio(false);
    }, 2000);
  };

  return (
    <div className="the-san-pham">
      <div className="hinh-anh-container">
        <img
          src={sanPham.hinhAnh}
          alt={sanPham.tenSanPham}
          className="hinh-anh-san-pham"
          onError={(e) => {
            e.target.src =
              "https://via.placeholder.com/300x300?text=Không+có+hình";
          }}
        />
        <div className="danh-muc-badge">{sanPham.danhMuc}</div>
        {sanPham.khuyenMai > 0 && (
          <div className="khuyen-mai-badge">
            <FontAwesomeIcon icon={faPercent} />-{sanPham.khuyenMai}%
          </div>
        )}
      </div>

      <div className="thong-tin-san-pham">
        <h3 className="ten-san-pham">{sanPham.tenSanPham}</h3>
        <p className="mo-ta-san-pham">{sanPham.moTa}</p>

        {/* Thông tin giá */}
        <div className="thong-tin-gia">
          <div className="gia-hien-tai">
            <span className="gia-san-pham">{dinhDangGia(sanPham.gia)}</span>
            {sanPham.khuyenMai > 0 && (
              <span className="gia-goc">{dinhDangGia(sanPham.giaGoc)}</span>
            )}
          </div>
        </div>

        {/* Thông tin đánh giá */}
        <div className="thong-tin-danh-gia">
          <div className="sao-danh-gia">
            {taoSaoDanhGia(sanPham.danhGia.diemTrungBinh)}
          </div>
          <span className="diem-danh-gia">{sanPham.danhGia.diemTrungBinh}</span>
          <span className="so-luong-danh-gia">
            ({sanPham.danhGia.soLuongDanhGia} đánh giá)
          </span>
        </div>

        {/* Thông tin số lượng đã bán */}
        <div className="thong-tin-ban-hang">
          <FontAwesomeIcon icon={faUsers} className="icon-ban-hang" />
          <span className="so-luong-da-ban">
            Đã bán {dinhDangSoLuongDaBan(sanPham.soLuongDaBan)} sản phẩm
          </span>
        </div>

        <button
          className={`nut-them-gio-hang ${vuaThemVaoGio ? "da-them" : ""}`}
          onClick={xuLyThemVaoGio}
        >
          {vuaThemVaoGio ? (
            <>
              <FontAwesomeIcon icon={faCheck} />
              Đã thêm vào giỏ
            </>
          ) : (
            <>
              <FontAwesomeIcon icon={faShoppingCart} />
              {sanPhamTrongGio
                ? `Trong giỏ (${sanPhamTrongGio.soLuong})`
                : "Thêm vào giỏ hàng"}
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default TheSanPham;
