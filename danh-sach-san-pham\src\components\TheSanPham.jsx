import './TheSanPham.css';

const TheSanPham = ({ sanPham }) => {
  // Hàm định dạng giá tiền
  const dinhDangGia = (gia) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND'
    }).format(gia);
  };

  return (
    <div className="the-san-pham">
      <div className="hinh-anh-container">
        <img 
          src={sanPham.hinhAnh} 
          alt={sanPham.tenSanPham}
          className="hinh-anh-san-pham"
          onError={(e) => {
            e.target.src = 'https://via.placeholder.com/300x300?text=Không+có+hình';
          }}
        />
        <div className="danh-muc-badge">{sanPham.danhMuc}</div>
      </div>
      
      <div className="thong-tin-san-pham">
        <h3 className="ten-san-pham">{sanPham.tenSanPham}</h3>
        <p className="mo-ta-san-pham">{sanPham.moTa}</p>
        
        <div className="gia-va-kho">
          <span className="gia-san-pham">{dinhDangGia(sanPham.gia)}</span>
          <span className={`ton-kho ${sanPham.tonKho < 10 ? 'it-hang' : ''}`}>
            Còn {sanPham.tonKho} sản phẩm
          </span>
        </div>
        
        <button 
          className="nut-them-gio-hang"
          disabled={sanPham.tonKho === 0}
        >
          {sanPham.tonKho === 0 ? 'Hết hàng' : 'Thêm vào giỏ hàng'}
        </button>
      </div>
    </div>
  );
};

export default TheSanPham;
