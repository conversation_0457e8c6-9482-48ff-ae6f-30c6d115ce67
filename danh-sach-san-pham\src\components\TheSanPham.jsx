import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faShoppingCart, faCheck } from "@fortawesome/free-solid-svg-icons";
import { useGioHang } from "../context/GioHangContext";
import { useState } from "react";
import "./TheSanPham.css";

const TheSanPham = ({ sanPham }) => {
  const { themVaoGio, danhSachSanPham } = useGioHang();
  const [vuaThemVaoGio, setVuaThemVaoGio] = useState(false);
  // Hàm định dạng giá tiền
  const dinhDangGia = (gia) => {
    return new Intl.NumberFormat("vi-VN", {
      style: "currency",
      currency: "VND",
    }).format(gia);
  };

  // Kiểm tra xem sản phẩm đã có trong giỏ hàng chưa
  const sanPhamTrongGio = danhSachSanPham.find(
    (item) => item.id === sanPham.id
  );

  // Hàm xử lý thêm vào giỏ hàng
  const xuLyThemVaoGio = () => {
    themVaoGio(sanPham);
    setVuaThemVaoGio(true);

    // Reset trạng thái sau 2 giây
    setTimeout(() => {
      setVuaThemVaoGio(false);
    }, 2000);
  };

  return (
    <div className="the-san-pham">
      <div className="hinh-anh-container">
        <img
          src={sanPham.hinhAnh}
          alt={sanPham.tenSanPham}
          className="hinh-anh-san-pham"
          onError={(e) => {
            e.target.src =
              "https://via.placeholder.com/300x300?text=Không+có+hình";
          }}
        />
        <div className="danh-muc-badge">{sanPham.danhMuc}</div>
      </div>

      <div className="thong-tin-san-pham">
        <h3 className="ten-san-pham">{sanPham.tenSanPham}</h3>
        <p className="mo-ta-san-pham">{sanPham.moTa}</p>

        <div className="gia-va-kho">
          <span className="gia-san-pham">{dinhDangGia(sanPham.gia)}</span>
          <span className={`ton-kho ${sanPham.tonKho < 10 ? "it-hang" : ""}`}>
            Còn {sanPham.tonKho} sản phẩm
          </span>
        </div>

        <button
          className={`nut-them-gio-hang ${vuaThemVaoGio ? "da-them" : ""}`}
          disabled={sanPham.tonKho === 0}
          onClick={xuLyThemVaoGio}
        >
          {sanPham.tonKho === 0 ? (
            "Hết hàng"
          ) : vuaThemVaoGio ? (
            <>
              <FontAwesomeIcon icon={faCheck} />
              Đã thêm vào giỏ
            </>
          ) : (
            <>
              <FontAwesomeIcon icon={faShoppingCart} />
              {sanPhamTrongGio
                ? `Trong giỏ (${sanPhamTrongGio.soLuong})`
                : "Thêm vào giỏ hàng"}
            </>
          )}
        </button>
      </div>
    </div>
  );
};

export default TheSanPham;
