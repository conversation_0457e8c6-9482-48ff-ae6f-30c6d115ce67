/* Nút giỏ hàng */
.nut-gio-hang {
  position: fixed;
  top: 20px;
  right: 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  transition: all 0.3s ease;
  z-index: 1000;
  font-size: 1.2rem;
}

.nut-gio-hang:hover {
  background: #0056b3;
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.so-luong-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #dc3545;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
  animation: bounce 0.3s ease;
}

@keyframes bounce {
  0%, 20%, 60%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  80% { transform: translateY(-5px); }
}

/* Overlay */
.gio-hang-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1001;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Sidebar giỏ hàng */
.gio-hang-sidebar {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
  z-index: 1002;
  transition: right 0.3s ease;
  display: flex;
  flex-direction: column;
}

.gio-hang-sidebar.mo {
  right: 0;
}

/* Header giỏ hàng */
.gio-hang-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
}

.gio-hang-header h2 {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

.nut-dong {
  background: none;
  border: none;
  font-size: 1.2rem;
  color: #666;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.nut-dong:hover {
  background: #e9ecef;
  color: #333;
}

/* Nội dung giỏ hàng */
.gio-hang-noi-dung {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Giỏ hàng trống */
.gio-hang-trong {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #666;
}

.gio-hang-trong svg {
  color: #ccc;
  margin-bottom: 20px;
}

.gio-hang-trong p {
  margin: 0 0 20px 0;
  font-size: 1.1rem;
}

.nut-tiep-tuc-mua {
  background: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.nut-tiep-tuc-mua:hover {
  background: #0056b3;
}

/* Danh sách sản phẩm trong giỏ */
.danh-sach-san-pham-gio {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.san-pham-gio {
  display: flex;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid #e1e5e9;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from { 
    opacity: 0;
    transform: translateX(20px);
  }
  to { 
    opacity: 1;
    transform: translateX(0);
  }
}

.hinh-anh-gio {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 8px;
  flex-shrink: 0;
}

.thong-tin-gio {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.ten-san-pham-gio {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
}

.gia-gio {
  margin: 0;
  color: #e74c3c;
  font-weight: 600;
  font-size: 0.95rem;
}

/* Điều khiển số lượng */
.dieu-khien-so-luong {
  display: flex;
  align-items: center;
  gap: 8px;
}

.nut-so-luong {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
}

.nut-so-luong:hover:not(:disabled) {
  background: #e9ecef;
  border-color: #adb5bd;
}

.nut-so-luong:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.so-luong-hien-thi {
  min-width: 30px;
  text-align: center;
  font-weight: 600;
  color: #333;
}

/* Giá và nút xóa */
.gia-va-xoa {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 10px;
}

.tong-gia-san-pham {
  margin: 0;
  font-weight: 700;
  color: #333;
  font-size: 1rem;
}

.nut-xoa {
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 4px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-size: 0.9rem;
}

.nut-xoa:hover {
  background: #c82333;
}

/* Footer giỏ hàng */
.gio-hang-footer {
  padding: 20px;
  border-top: 1px solid #e1e5e9;
  background: #f8f9fa;
}

.tong-tien {
  margin-bottom: 20px;
  text-align: center;
}

.tong-tien h3 {
  margin: 0;
  color: #333;
  font-size: 1.3rem;
}

.cac-nut-hanh-dong {
  display: flex;
  gap: 10px;
}

.nut-xoa-toan-bo,
.nut-thanh-toan {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.nut-xoa-toan-bo {
  background: #6c757d;
  color: white;
}

.nut-xoa-toan-bo:hover {
  background: #5a6268;
}

.nut-thanh-toan {
  background: #28a745;
  color: white;
}

.nut-thanh-toan:hover {
  background: #218838;
}

/* Responsive */
@media (max-width: 480px) {
  .gio-hang-sidebar {
    width: 100vw;
    right: -100vw;
  }
  
  .nut-gio-hang {
    width: 50px;
    height: 50px;
    font-size: 1rem;
  }
  
  .so-luong-badge {
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
  }
}
