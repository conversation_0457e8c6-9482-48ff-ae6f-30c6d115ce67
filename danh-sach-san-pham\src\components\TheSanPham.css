.the-san-pham {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.the-san-pham:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.hinh-anh-container {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hinh-anh-san-pham {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.the-san-pham:hover .hinh-anh-san-pham {
  transform: scale(1.05);
}

.danh-muc-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
}

.thong-tin-san-pham {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.ten-san-pham {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 10px 0;
  line-height: 1.3;
}

.mo-ta-san-pham {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0 0 15px 0;
  flex: 1;
}

.gia-va-kho {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.gia-san-pham {
  font-size: 1.1rem;
  font-weight: 700;
  color: #e74c3c;
}

.ton-kho {
  font-size: 0.85rem;
  color: #28a745;
  font-weight: 500;
}

.ton-kho.it-hang {
  color: #ffc107;
}

.nut-them-gio-hang {
  width: 100%;
  padding: 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.nut-them-gio-hang:hover:not(:disabled) {
  background: #0056b3;
}

.nut-them-gio-hang:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .the-san-pham {
    margin-bottom: 20px;
  }
  
  .hinh-anh-container {
    height: 200px;
  }
  
  .thong-tin-san-pham {
    padding: 15px;
  }
  
  .ten-san-pham {
    font-size: 1.1rem;
  }
}
