.the-san-pham {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.the-san-pham:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.hinh-anh-container {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.hinh-anh-san-pham {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.the-san-pham:hover .hinh-anh-san-pham {
  transform: scale(1.05);
}

.danh-muc-badge {
  position: absolute;
  top: 12px;
  left: 12px;
  background: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
}

.khuyen-mai-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  background: #dc3545;
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 4px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }

  100% {
    transform: scale(1);
  }
}

.thong-tin-san-pham {
  padding: 20px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.ten-san-pham {
  font-size: 1.25rem;
  font-weight: 600;
  color: #333;
  margin: 0 0 10px 0;
  line-height: 1.3;
}

.mo-ta-san-pham {
  color: #666;
  font-size: 0.9rem;
  line-height: 1.5;
  margin: 0 0 15px 0;
  flex: 1;
}

/* Thông tin giá */
.thong-tin-gia {
  margin-bottom: 12px;
}

.gia-hien-tai {
  display: flex;
  align-items: center;
  gap: 10px;
}

.gia-san-pham {
  font-size: 1.2rem;
  font-weight: 700;
  color: #e74c3c;
}

.gia-goc {
  font-size: 0.9rem;
  color: #999;
  text-decoration: line-through;
  font-weight: 500;
}

/* Thông tin đánh giá */
.thong-tin-danh-gia {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.sao-danh-gia {
  display: flex;
  gap: 2px;
}

.sao-day {
  color: #ffc107;
  font-size: 0.9rem;
}

.sao-nua {
  color: #ffc107;
  font-size: 0.9rem;
}

.sao-rong {
  color: #e9ecef;
  font-size: 0.9rem;
}

.diem-danh-gia {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.so-luong-danh-gia {
  color: #666;
  font-size: 0.85rem;
}

/* Thông tin bán hàng */
.thong-tin-ban-hang {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 15px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #28a745;
}

.icon-ban-hang {
  color: #28a745;
  font-size: 0.9rem;
}

.so-luong-da-ban {
  color: #666;
  font-size: 0.85rem;
  font-weight: 500;
}

.nut-them-gio-hang {
  width: 100%;
  padding: 12px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.nut-them-gio-hang:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

.nut-them-gio-hang:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.nut-them-gio-hang.da-them {
  background: #28a745;
  animation: successPulse 0.6s ease;
}

.nut-them-gio-hang.da-them:hover {
  background: #218838;
}

@keyframes successPulse {
  0% {
    transform: scale(1);
    background: #007bff;
  }

  50% {
    transform: scale(1.05);
    background: #28a745;
  }

  100% {
    transform: scale(1);
    background: #28a745;
  }
}

@media (max-width: 768px) {
  .the-san-pham {
    margin-bottom: 20px;
  }

  .hinh-anh-container {
    height: 200px;
  }

  .thong-tin-san-pham {
    padding: 15px;
  }

  .ten-san-pham {
    font-size: 1.1rem;
  }
}