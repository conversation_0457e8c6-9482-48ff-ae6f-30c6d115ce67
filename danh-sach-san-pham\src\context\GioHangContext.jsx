import { createContext, useContext, useReducer, useEffect } from 'react';

// Tạo Context cho giỏ hàng
const GioHangContext = createContext();

// Các action types cho reducer
const LOAI_HANH_DONG = {
  THEM_VAO_GIO: 'THEM_VAO_GIO',
  XOA_KHOI_GIO: 'XOA_KHOI_GIO',
  CAP_NHAT_SO_LUONG: 'CAP_NHAT_SO_LUONG',
  XOA_TOAN_BO: 'XOA_TOAN_BO',
  TAI_GIO_HANG: 'TAI_GIO_HANG'
};

// Reducer để quản lý state của giỏ hàng
const gioHangReducer = (state, action) => {
  switch (action.type) {
    case LOAI_HANH_DONG.THEM_VAO_GIO: {
      const { sanPham, soLuong = 1 } = action.payload;
      const sanPhamTonTai = state.danhSachSanPham.find(item => item.id === sanPham.id);
      
      if (sanPhamTonTai) {
        // Nếu sản phẩm đã có trong giỏ, tăng số lượng
        return {
          ...state,
          danhSachSanPham: state.danhSachSanPham.map(item =>
            item.id === sanPham.id
              ? { ...item, soLuong: Math.min(item.soLuong + soLuong, sanPham.tonKho) }
              : item
          )
        };
      } else {
        // Nếu sản phẩm chưa có, thêm mới
        return {
          ...state,
          danhSachSanPham: [...state.danhSachSanPham, { ...sanPham, soLuong }]
        };
      }
    }
    
    case LOAI_HANH_DONG.XOA_KHOI_GIO: {
      return {
        ...state,
        danhSachSanPham: state.danhSachSanPham.filter(item => item.id !== action.payload.id)
      };
    }
    
    case LOAI_HANH_DONG.CAP_NHAT_SO_LUONG: {
      const { id, soLuong } = action.payload;
      if (soLuong <= 0) {
        // Nếu số lượng <= 0, xóa sản phẩm khỏi giỏ
        return {
          ...state,
          danhSachSanPham: state.danhSachSanPham.filter(item => item.id !== id)
        };
      }
      
      return {
        ...state,
        danhSachSanPham: state.danhSachSanPham.map(item =>
          item.id === id
            ? { ...item, soLuong: Math.min(soLuong, item.tonKho) }
            : item
        )
      };
    }
    
    case LOAI_HANH_DONG.XOA_TOAN_BO: {
      return {
        ...state,
        danhSachSanPham: []
      };
    }
    
    case LOAI_HANH_DONG.TAI_GIO_HANG: {
      return {
        ...state,
        danhSachSanPham: action.payload || []
      };
    }
    
    default:
      return state;
  }
};

// State ban đầu của giỏ hàng
const trangThaiBanDau = {
  danhSachSanPham: [],
  dangMo: false
};

// Provider component
export const GioHangProvider = ({ children }) => {
  const [state, dispatch] = useReducer(gioHangReducer, trangThaiBanDau);

  // Lưu giỏ hàng vào localStorage khi có thay đổi
  useEffect(() => {
    localStorage.setItem('gioHang', JSON.stringify(state.danhSachSanPham));
  }, [state.danhSachSanPham]);

  // Tải giỏ hàng từ localStorage khi khởi tạo
  useEffect(() => {
    const gioHangDaLuu = localStorage.getItem('gioHang');
    if (gioHangDaLuu) {
      try {
        const danhSach = JSON.parse(gioHangDaLuu);
        dispatch({ type: LOAI_HANH_DONG.TAI_GIO_HANG, payload: danhSach });
      } catch (error) {
        console.error('Lỗi khi tải giỏ hàng từ localStorage:', error);
      }
    }
  }, []);

  // Các hàm helper
  const themVaoGio = (sanPham, soLuong = 1) => {
    dispatch({ 
      type: LOAI_HANH_DONG.THEM_VAO_GIO, 
      payload: { sanPham, soLuong } 
    });
  };

  const xoaKhoiGio = (id) => {
    dispatch({ 
      type: LOAI_HANH_DONG.XOA_KHOI_GIO, 
      payload: { id } 
    });
  };

  const capNhatSoLuong = (id, soLuong) => {
    dispatch({ 
      type: LOAI_HANH_DONG.CAP_NHAT_SO_LUONG, 
      payload: { id, soLuong } 
    });
  };

  const xoaToanBo = () => {
    dispatch({ type: LOAI_HANH_DONG.XOA_TOAN_BO });
  };

  const moGioHang = () => {
    dispatch({ type: 'MO_GIO_HANG' });
  };

  const dongGioHang = () => {
    dispatch({ type: 'DONG_GIO_HANG' });
  };

  // Tính toán các giá trị
  const tongSoLuong = state.danhSachSanPham.reduce((tong, item) => tong + item.soLuong, 0);
  const tongTien = state.danhSachSanPham.reduce((tong, item) => tong + (item.gia * item.soLuong), 0);

  const giaTriContext = {
    danhSachSanPham: state.danhSachSanPham,
    dangMo: state.dangMo,
    tongSoLuong,
    tongTien,
    themVaoGio,
    xoaKhoiGio,
    capNhatSoLuong,
    xoaToanBo,
    moGioHang,
    dongGioHang
  };

  return (
    <GioHangContext.Provider value={giaTriContext}>
      {children}
    </GioHangContext.Provider>
  );
};

// Hook để sử dụng context
export const useGioHang = () => {
  const context = useContext(GioHangContext);
  if (!context) {
    throw new Error('useGioHang phải được sử dụng trong GioHangProvider');
  }
  return context;
};
